// ignore_for_file: constant_identifier_names
import '../generated/l10n.dart';

String parseErrorKey(String errorKey) {
  switch (errorKey) {
    case 'payment_required':
      return S.current.payment_required;
    case 'token_expired':
      return S.current.token_expired;
    case 'invalid_token':
      return S.current.invalid_token;
    case 'AUDIO_PROCESS_ERR':
      return S.current.audio_process_err;
    case 'YT_PROCESS_ERR':
      return S.current.yt_process_err;
    case 'INV_YT_URL':
      return S.current.inv_yt_url;
    case 'YT_CREDIT_ERR':
      return S.current.yt_credit_err;
    case 'YT_CREDIT_USE_ERR':
      return S.current.yt_credit_use_err;
    case 'INV_AUDIO':
      return S.current.inv_audio;
    case 'AUDIO_LENGTH_ERR':
      return S.current.audio_length_err;
    case 'NO_RECORDING_CREDIT':
      return S.current.no_recording_credit;
    case 'NO_UPLOAD_CREDIT':
      return S.current.no_upload_credit;
    case 'FILE_SIZE_ERR':
      return S.current.file_size_err;
    case 'NO_INPUT':
      return S.current.no_input;
    case 'SERVER_ERROR':
      return S.current.unknown_server_error;
    case 'DB_ERR':
      return S.current.db_err;
    case 'NOTE_404':
      return S.current.note_404;
    case 'NO_SUMMARY':
      return S.current.no_summary;
    case 'NO_TRANSCRIPT':
      return S.current.no_transcript;
    case 'TASK_CREATE_ERR':
      return S.current.task_create_err;
    case 'UPDATE_FAILED':
      return S.current.update_failed;
    case 'YT_LENGTH_ERR':
      return S.current.yt_length_err;
    case 'no_internet':
      return S.current.no_internet;
    case 'TIME_OUT':
      return S.current.time_out;
    case 'extract_fail':
      return S.current.cannot_extract_text_from_pdf;
    case 'TASK_NOT_FOUND':
      return S.current.brief_service_disruption;
    case 'INV_WEB_URL':
      return S.current.unable_to_extract_web_url;
    case 'REFERRAL_NOT_FOUND':
      return S.current.referral_not_found;
    case 'REFERRAL_SELF_USE':
      return S.current.referral_self_use;
    case 'USER_NOT_FOUND':
      return S.current.user_not_found;
    case 'REFERRAL_ALREADY_USED':
      return S.current.referral_already_used;
    case 'REFERRAL_TIME_EXPIRED':
      return S.current.referral_time_expired;
    case 'SERVER_ERR':
      return S.current.server_err;
    case 'REFERRAL_VALIDATION_ERR':
      return S.current.referral_validation_err;
    case 'BULK_MOVE_FAILED':
      return "Bulk move operation failed with errors";
    case 'MAX_FOLDER_DEPTH_REACHED':
      return "Maximum folder depth reached. Cannot create folder beyond level 3 (0-indexed).";
    default:
      return S.current.default_error;
  }
}

enum ErrorKey {
  payment_required,
  daily_rewards_limit_reached,
  image_too_large,
  image_generation_failed,
  image_quality_too_low,
  multiple_people_detected,
  no_person_detected,
  child_detected,
  database_error,
  token_expired,
  invalid_token,
  blurred_output_image,
  audio_process_err,
  yt_process_err,
  inv_yt_url,
  yt_credit_err,
  yt_credit_use_err,
  inv_audio,
  audio_length_err,
  no_recording_credit,
  no_upload_credit,
  file_size_err,
  no_input,
  server_error,
  db_err,
  note_404,
  no_summary,
  no_transcript,
  task_create_err,
  server_err,
  update_failed,
  yt_length_err,
  no_internet,
  referral_not_found,
  referral_self_use,
  user_not_found,
  referral_already_used,
  referral_time_expired,
  referral_validation_err,
  login_to_note_xbe_fail
}
