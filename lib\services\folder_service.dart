import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class FolderService {
  final FolderApiService _apiService =
      GetIt.instance.get<FolderApiServiceImpl>();

  // Query methods - delegate to HiveFolderService
  Future<void> getAllFolders() async {
    final folderList = await _apiService.getAllFolders();
    HiveFolderService.saveAllFolders(folders: folderList);
  }

  // Create root folder
  Future<void> createRootFolder({required String folderName}) async {
    try {
      final folderModel = await _apiService.createRootFolder(folderName);
      await HiveFolderService.saveRootFolder(folderModel);
    } catch (e) {
      throw Exception('Failed to create folder: $e');
    }
  }

  // Create subfolder
  Future<FolderModel> createSubfolder({
    required String name,
    required String parentFolderId,
  }) async {
    try {
      // Validate parent exists
      final parentFolder =
          HiveFolderService.getFolderById(folderBackendId: parentFolderId);
      if (parentFolder == null) {
        throw Exception('Parent folder not found');
      }

      if (parentFolder.level >= FolderModel.maxLevel) {
        throw Exception(
            'Cannot create subfolder: Maximum level (${FolderModel.maxLevel}) reached');
      }

      // Call API first
      final subfolder = await _apiService.createSubFolder(name, parentFolderId);

      // Save the new subfolder to Hive immediately using backendId as key
      await HiveFolderService.saveRootFolder(subfolder);

      return subfolder;
    } catch (e) {
      rethrow;
    }
  }

  // Edit folder
  Future<FolderModel> editFolderName({
    required String folderId,
    required String newName,
  }) async {
    try {
      // Call API first
      final updatedFolder = await _apiService.editFolder(folderId, newName);

      // Update Hive on success
      await HiveFolderService.renameFolderById(
        folderLocalId: folderId,
        newName: newName,
      );

      return updatedFolder;
    } catch (e) {
      throw Exception('Failed to edit folder: $e');
    }
  }

  // Delete folder
  Future<bool> deleteFolder({
    required String folderId,
    bool deleteNotes = false,
  }) async {
    try {
      // Call API first
      final success = await _apiService.deleteFolder(folderId: folderId);

      if (success) {
        // Update Hive on success
        await HiveFolderService.deleteFolder(
          folderLocalId: folderId,
          deleteNotes: deleteNotes,
        );
      }

      return success;
    } catch (e) {
      throw Exception('Failed to delete folder: $e');
    }
  }

  // Move folder (deprecated - use bulkMoveNotesToFolder instead)
  @Deprecated('Use bulkMoveNotesToFolder instead')
  Future<void> moveFolder({
    required String folderId,
    required String targetFolderId,
  }) async {
    // Redirect to bulk move API
    await bulkMoveNotesToFolder(
      targetFolderId: targetFolderId,
      folderIds: [folderId],
      noteIds: [],
    );
  }

  // Bulk operations
  Future<void> bulkDeleteItems({
    required List<String> folderIds,
    required List<String> noteIds,
    required bool deleteNotes,
  }) async {
    try {
      // Call API first
      await _apiService.bulkDeleteItems(
        folderIds: folderIds,
        noteIds: noteIds,
        deleteNotes: deleteNotes,
      );

      // Update Hive on success
      for (final folderId in folderIds) {
        await HiveFolderService.deleteFolder(
          folderLocalId: folderId,
          deleteNotes: deleteNotes,
        );
      }

      // Delete notes directly
      final noteBox = HiveService().noteBox;
      for (final noteId in noteIds) {
        await noteBox.delete(noteId);
      }
    } catch (e) {
      throw Exception('Failed to bulk delete: $e');
    }
  }

  Future<void> bulkMoveNotesToFolder({
    required String targetFolderId,
    required List<String> folderIds,
    required List<String> noteIds,
  }) async {
    // Call API
    try {
      await _apiService.bulkMoveNotesToFolder(
        targetFolderId: targetFolderId,
        folderIds: folderIds,
        noteIds: noteIds,
      );
    } catch (apiError) {
      rethrow;
    }

    // Move folders to target folder
    await getAllFolders();

    // Move notes to target folder
    if (noteIds.isEmpty) return;

    final targetFolder =
        HiveFolderService.getFolderById(folderBackendId: targetFolderId);
    if (targetFolder == null) return;

    final noteBox = HiveService().noteBox;

    await Future.wait(noteIds.map((noteId) async {
      final note = noteBox.get(noteId);
      if (note != null) {
        await HiveFolderService.addNoteToFolder(
          note: note,
          folderBackendId: targetFolderId,
        );
      }
    }));
  }

  // Note operations
  Future<void> addNoteToFolder({
    required NoteModel note,
    required String folderBackendId,
  }) async {
    await HiveFolderService.addNoteToFolder(
      note: note,
      folderBackendId: folderBackendId,
    );
  }

  Future<void> getFolderDetail(String folderId) async {
    final folderModel = await _apiService.getFolderDetail(folderId);
    await HiveFolderService.saveFolderDetail(folderModel);
  }

  List<FolderModel> getRootFolders() => HiveFolderService.getRootFolders();

  List<FolderModel> getSubfolders({required String folderBackendId}) =>
      HiveFolderService.getSubFolders(folderBackendId: folderBackendId);

  FolderModel? getFolderById({required String folderBackendId}) =>
      HiveFolderService.getFolderById(folderBackendId: folderBackendId);

  String getFolderNameById(String folderBackendId) =>
      HiveFolderService.getNameFolderByBackEndId(folderBackendId);

  Future<List<NoteModel>> getNotesInFolder({required String folderBackendId}) =>
      HiveFolderService.getNotesInFolder(folderBackendId: folderBackendId);

  Future<List<NoteModel>> getNotesFailed({required String userId}) =>
      HiveFolderService.getNotesFailed(userId: userId);

  // Advanced query methods
  List<FolderModel> searchFolders(String query) =>
      HiveFolderService.searchFolders(query);

  List<FolderModel> getFoldersAtLevel(int level) =>
      HiveFolderService.getFoldersAtLevel(level);
}
